<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :notConfirmSmsList="queryProps.notConfirmSmsList"
    :label-width="labelWidth"
    :deviceDmrId="deviceDmrId"
    :userRid="userRid"
    :head="dthead"
    :name="dataTableName"
    :history-table="true"
    :replaceHistoryTableData="type === 1 ? replaceNotConfirmTableData : undefined"
    :parse-request-data="parseRequestData"
    :afterRequestEnd="showDataTable"
    @clear-dt="clearNotConfirmDt"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item :label="$t(`${'dialog.senderDevice'}`)" prop="deviceRid">
        <DataTableElSelect v-model="deviceDmrId" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.dmrId" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item :label="$t(`${'dataTable.senderName'}`)" prop="userRid">
        <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
    <template #queryBtn>
      <div class="max-md:shrink max-md:grow flex flex-col md:flex-row gap-1.5">
        <el-button type="success" class="smsBtn flex-1 md:ml-4" @click="queryFunction()" v-text="$t('dialog.querySentSMS')" />
        <el-button type="primary" class="smsBtn flex-1" @click="queryFunction(1)" v-text="$t('dialog.queryNotSentSMS')" />
      </div>
    </template>
    <template v-if="type === 1" #not-confirm-sms>
      <dataTablesVue3
        ref="notConfirmDt"
        :key="notConfirmSmsDataTable.name"
        :name="notConfirmSmsDataTable.name"
        :head="notConfirmSmsDataDthead"
        :data="notConfirmSmsDataTable.body"
        :historyTable="true"
      />
    </template>
  </historyCommon>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'
  import bfNotify from '@/utils/notify'
  import dataTablesVue3 from '@/components/common/dataTablesVue3.vue'

  export default {
    name: 'VsmsHistory',
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_sms_history_list',
          notConfirmSmsList: 'db_not_confirm_sms_list',
          cmd: 56,
        },
        dataTableName: 'smsHistoryTable',
        notConfirmSmsBodyCache: [],
        deviceDmrId: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
        //判断是否是查询短信历史还是查询未确认短信历史 查询短信历史: 0; 查询未确认短信历史 1;
        type: 0,
        notConfirmSmsDataTable: {
          name: 'notConfirmSmsHistoryTable',
          body: [],
        },
      }
    },
    methods: {
      queryFunction(type = 0) {
        this.type = type
        this.$refs.hisCom.queryFunc(type)
      },
      showDataTable(dbListOfProtoTypeName, tableBody) {
        const checkDataLength = () => {
          if (dbListOfProtoTypeName !== this.queryProps.dbListName) {
            this.notConfirmSmsDataTable.body = tableBody
          }
          if (tableBody.length < 0) {
            bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
          }
        }

        checkDataLength()
      },
      removeDataTableData() {
        this.deviceDmrId = ''
        this.userRid = ''
        this.notConfirmSmsDataTable.body = []
      },
      parseRequestData(item, dbListOfProtoTypeName) {
        // 系统中心下发
        if (item.senderDmrid === '00000000') {
          const userData = bfglob.guserData.get(item.senderUserRid)
          if (userData) {
            item.orgShortName = bfglob.guserData.getOrgNameByKey(userData.orgId)
          }
          item.senderName = ''
        } else {
          const device = bfglob.gdevices.getDataByIndex(item.senderDmrid)
          if (!device) {
            bfglob.console.error('没有此对讲机', item.senderDmrid)
            return
          }
          item.orgShortName = device.orgShortName
          item.senderName = device.selfId
        }

        const repeater = bfglob.gcontrollers.getDataByIndex(item.receiveRepeater)
        item.repeaterName = repeater && repeater.selfId ? repeater.selfId : ''

        // 目标是全呼
        if (item.targetDmrid === bfglob.fullCallDmrId) {
          item.receiveName = this.$t('dialog.fullCall')
        } else if (item.targetDmrid === bfglob.systemDmrId) {
          // 目标是系统中心
          item.receiveName = this.$t('dialog.systemCenter')
        } else {
          let receiveName = item.targetDmrid
          // 目标是组呼
          let targetObj = bfglob.gorgData.getDataByIndex(item.targetDmrid)
          if (targetObj) {
            receiveName = targetObj.orgShortName
          } else {
            // 目标是单呼
            targetObj = bfglob.gdevices.getDataByIndex(item.targetDmrid)
            receiveName = targetObj ? targetObj.selfId : receiveName
          }

          item.receiveName = receiveName
        }

        item.senderUserName = bfglob.guserData.getUserNameByKey(item.senderUserRid)
        if (typeof item.confirmTime === 'string') {
          item.confirmTime = item.confirmTime.startsWith('200') || !item.confirmTime ? '' : item.confirmTime
        }

        // if (dbListOfProtoTypeName === this.queryProps.dbListName) {
        //   // this.bodyCache.push(item)
        //   return item
        // } else {
        //   this.notConfirmSmsBodyCache.push(item)
        // }
        return item
      },
      clearNotConfirmDt() {
        this.$refs.notConfirmDt?.instance.clear()
      },
      replaceNotConfirmTableData(rows) {
        this.$refs.notConfirmDt.replaceTableData(rows)
      },
    },
    components: {
      dataTablesVue3,
      historyCommon,
    },
    computed: {
      contentClass() {
        return this.isMobile ? 'is-mobile ' : ''
      },
      labelWidth() {
        return this.isFR ? '120px' : this.isEN ? '90px' : '80px'
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.senderDevice'),
            data: 'senderName',
            width: '100px',
            render: (data, type, row, meta) => {
              if (row.senderDmrid === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.senderName
              }
            },
          },
          {
            title: this.$t('dataTable.senderName'),
            data: 'senderUserName',
            width: '100px',
          },
          {
            title: this.$t('dialog.receiveDevice'),
            data: 'receiveName',
            width: '120px',
          },
          {
            title: this.$t('dialog.smsContent'),
            data: 'smsContent',
            width: '300px',
          },
          {
            title: this.$t('dialog.smsType'),
            data: null,
            width: '100px',
            render: (data, type, row, meta) => {
              switch (row.smsType) {
                case '12':
                  return this.$t('dialog.autoPlaySms')
                default:
                  return this.$t('dialog.textInfo')
              }
            },
          },
          {
            title: this.$t('dataTable.repeater'),
            data: 'repeaterName',
            width: this.isEN ? '135px' : '100px',
            render: (data, type, row, meta) => {
              if (row.receiveRepeater === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.repeaterName
              }
            },
          },
          {
            title: this.$t('dialog.sendTime'),
            data: 'startTime',
            width: '130px',
          },
          {
            title: this.$t('dataTable.confirmTime'),
            data: 'confirmTime',
            width: '130px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '120px',
          },
        ]
      },
      notConfirmSmsDataDthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.senderDevice'),
            data: 'senderName',
            width: '100px',
            render: (data, type, row, meta) => {
              if (row.senderDmrid === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.senderName
              }
            },
          },
          {
            title: this.$t('dataTable.senderName'),
            data: 'senderUserName',
            width: '100px',
          },
          {
            title: this.$t('dialog.receiveDevice'),
            data: 'receiveName',
            width: '120px',
          },
          {
            title: this.$t('dialog.smsContent'),
            data: 'smsContent',
            width: '300px',
          },
          {
            title: this.$t('dialog.smsType'),
            data: null,
            width: '100px',
            render: (data, type, row, meta) => {
              switch (row.smsType) {
                case '12':
                  return this.$t('dialog.autoPlaySms')
                default:
                  return this.$t('dialog.textInfo')
              }
            },
          },
          {
            title: this.$t('dataTable.repeater'),
            data: 'repeaterName',
            width: this.isEN ? '135px' : '100px',
            render: (data, type, row, meta) => {
              if (row.receiveRepeater === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.repeaterName
              }
            },
          },
          {
            title: this.$t('dialog.sendTime'),
            data: 'startTime',
            width: '130px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '120px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.smsHistory')
      },
      isEN() {
        return this.$i18n.locale === 'en'
      },
    },
  }
</script>

<style>
  .MobileLongLabel {
    .el-form-item__label {
      height: 32px;
      line-height: 1;

      @media (min-width: 768px) {
        transform: translateY(0.5rem);
      }
    }
  }
</style>
